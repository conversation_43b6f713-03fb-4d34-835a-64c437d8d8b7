import 'package:flutter/material.dart';
import 'package:xly/xly.dart';

import 'controllers/gobang_controller.dart';
import 'pages/gobang_game_page.dart';
import 'rust/frb_generated.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await RustLib.init();

  // 使用xly包的应用初始化，集成GetX和ScreenUtil
  await MyApp.initialize(
    appName: '五子棋',
    designSize: const Size(800, 560), // 调整设计尺寸，适配窗口大小
    routes: [
      MyRoute<GobangController>(
        path: Routes.gobang,
        page: const GobangGamePage(),
        controller: () => GobangController(),
      ),
    ],
    theme: ThemeData(
      primarySwatch: Colors.brown,
      fontFamily: 'Microsoft YaHei', // 中文字体
      useMaterial3: true,
    ),
  );
}

class Routes {
  static const String gobang = '/gobang';
}
