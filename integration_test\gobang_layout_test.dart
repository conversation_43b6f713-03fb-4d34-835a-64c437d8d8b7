import 'package:flutter/material.dart';
import 'package:xly/xly.dart';

void main() {
  runApp(const SimpleGobangApp());
}

class SimpleGobangApp extends StatelessWidget {
  const SimpleGobangApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1400, 900), // 调整后的设计尺寸
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: '五子棋布局测试',
          theme: ThemeData(
            primarySwatch: Colors.brown,
            fontFamily: 'Microsoft YaHei',
            useMaterial3: true,
          ),
          home: const SimpleGobangPage(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}

class SimpleGobangPage extends StatefulWidget {
  const SimpleGobangPage({super.key});

  @override
  State<SimpleGobangPage> createState() => _SimpleGobangPageState();
}

class _SimpleGobangPageState extends State<SimpleGobangPage> {
  // 简单的棋盘状态，0=空，1=黑子，2=白子
  List<List<int>> board = List.generate(15, (i) => List.generate(15, (j) => 0));
  int currentPlayer = 1; // 1=黑子，2=白子

  void makeMove(int row, int col) {
    if (board[row][col] == 0) {
      setState(() {
        board[row][col] = currentPlayer;
        currentPlayer = currentPlayer == 1 ? 2 : 1;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5DC), // 米色背景
      appBar: AppBar(
        title: Text(
          '五子棋',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF8B4513),
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            // 计算可用空间，考虑padding
            final availableWidth = constraints.maxWidth - 32.w; // 左右padding
            final availableHeight = constraints.maxHeight - 32.h; // 上下padding

            // 计算棋盘区域的最大尺寸（4:1布局中的4部分）
            final boardAreaWidth = (availableWidth - 16.w) * 4 / 5; // 减去中间间距

            // 棋盘应该是正方形，取宽度和高度的最小值
            final maxBoardSize = [
              boardAreaWidth,
              availableHeight,
            ].reduce((a, b) => a < b ? a : b);

            return Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 左侧棋盘区域 (4/5)
                  Expanded(
                    flex: 4,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Center(
                        child: SizedBox(
                          width: maxBoardSize,
                          height: maxBoardSize,
                          child: SimpleGobangBoard(
                            board: board,
                            onTap: makeMove,
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 16.w),

                  // 右侧信息面板区域 (1/5)
                  Expanded(
                    flex: 1,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: const Color(0xFF8B4513)),
                      ),
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '五子棋',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF8B4513),
                            ),
                          ),
                          SizedBox(height: 16.h),

                          Container(
                            padding: EdgeInsets.all(12.w),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF5F5DC),
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(
                                color: const Color(0xFFD2B48C),
                              ),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  '当前玩家: ',
                                  style: TextStyle(fontSize: 14.sp),
                                ),
                                Container(
                                  width: 20.w,
                                  height: 20.w,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: currentPlayer == 1
                                        ? Colors.black
                                        : Colors.white,
                                    border: Border.all(color: Colors.grey),
                                  ),
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  currentPlayer == 1 ? '黑子' : '白子',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: 16.h),

                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                board = List.generate(
                                  15,
                                  (i) => List.generate(15, (j) => 0),
                                );
                                currentPlayer = 1;
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF8B4513),
                              foregroundColor: Colors.white,
                              minimumSize: Size(double.infinity, 40.h),
                            ),
                            child: Text(
                              '重新开始',
                              style: TextStyle(fontSize: 14.sp),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

class SimpleGobangBoard extends StatefulWidget {
  final List<List<int>> board;
  final Function(int, int) onTap;

  const SimpleGobangBoard({
    super.key,
    required this.board,
    required this.onTap,
  });

  @override
  State<SimpleGobangBoard> createState() => _SimpleGobangBoardState();
}

class _SimpleGobangBoardState extends State<SimpleGobangBoard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFD2B48C), // 浅棕色木质感
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8.r,
            offset: Offset(2.w, 2.h),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: GestureDetector(
          onTapDown: (details) => _onTapDown(details),
          behavior: HitTestBehavior.opaque,
          child: CustomPaint(
            painter: SimpleBoardPainter(),
            child: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: _buildPieces(),
            ),
          ),
        ),
      ),
    );
  }

  void _onTapDown(TapDownDetails details) {
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final localPosition = details.localPosition;
    final size = renderBox.size;
    const boardSize = 15;

    // 确保棋盘是正方形，取宽度和高度的最小值
    final boardDimension = size.width < size.height ? size.width : size.height;
    final cellSize = boardDimension / (boardSize - 1);

    // 转换为网格坐标
    final row = (localPosition.dy / cellSize).round();
    final col = (localPosition.dx / cellSize).round();

    // 检查坐标是否有效
    if (row >= 0 && row < boardSize && col >= 0 && col < boardSize) {
      widget.onTap(row, col);
    }
  }

  Widget _buildPieces() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final pieces = <Widget>[];
        const boardSize = 15;
        final size = constraints.biggest;

        // 确保棋盘是正方形，取宽度和高度的最小值
        final boardDimension = size.width < size.height
            ? size.width
            : size.height;
        final cellSize = boardDimension / (boardSize - 1);
        final pieceSize = cellSize * 0.8; // 棋子大小为格子的80%

        for (int row = 0; row < boardSize; row++) {
          for (int col = 0; col < boardSize; col++) {
            final pieceType = widget.board[row][col];
            if (pieceType != 0) {
              pieces.add(
                Positioned(
                  left: col * cellSize - pieceSize / 2,
                  top: row * cellSize - pieceSize / 2,
                  child: Container(
                    width: pieceSize,
                    height: pieceSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: pieceType == 1
                          ? const RadialGradient(
                              colors: [Color(0xFF404040), Color(0xFF000000)],
                              stops: [0.3, 1.0],
                            )
                          : const RadialGradient(
                              colors: [Color(0xFFFFFFFF), Color(0xFFE0E0E0)],
                              stops: [0.3, 1.0],
                            ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.5),
                          blurRadius: 4.r,
                          offset: Offset(1.w, 1.h),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }
          }
        }

        return Stack(children: pieces);
      },
    );
  }
}

class SimpleBoardPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color =
          const Color(0xFF8B4513) // 深棕色线条
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    const boardSize = 15;

    // 确保棋盘是正方形，取宽度和高度的最小值
    final boardDimension = size.width < size.height ? size.width : size.height;
    final cellSize = boardDimension / (boardSize - 1);

    // 绘制网格线
    for (int i = 0; i < boardSize; i++) {
      // 水平线
      canvas.drawLine(
        Offset(0, i * cellSize),
        Offset(boardDimension, i * cellSize),
        paint,
      );

      // 垂直线
      canvas.drawLine(
        Offset(i * cellSize, 0),
        Offset(i * cellSize, boardDimension),
        paint,
      );
    }

    // 绘制天元和星位
    final starPaint = Paint()
      ..color = const Color(0xFF8B4513)
      ..style = PaintingStyle.fill;

    // 天元 (中心点)
    final center = boardSize ~/ 2;
    canvas.drawCircle(
      Offset(center * cellSize, center * cellSize),
      3.0,
      starPaint,
    );

    // 四个星位
    final starPositions = [3, 11]; // 对应15x15棋盘的星位
    for (final row in starPositions) {
      for (final col in starPositions) {
        canvas.drawCircle(
          Offset(col * cellSize, row * cellSize),
          2.0,
          starPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
